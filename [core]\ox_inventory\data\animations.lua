return {
    anim = {
        ['eating'] = { 
            dict = 'mp_player_inteat@burger', 
            clip = 'mp_player_int_eat_burger_fp' 
        },
        --[[['fork'] = { 
            dict = 'anim@eat@fork', 
            clip = 'fork_clip' 
        },]] --only use this if you have Bzzz Props 
        ['alca'] = { 
            dict = 'anim@eat@fork', 
            clip = 'fork_clip' 
        },
        ['drink'] = { 
            dict = 'amb@world_human_drinking@coffee@male@idle_a', 
            clip = 'idle_c' 
        },
        ['drink2'] = { 
            dict = 'mp_player_intdrink', 
            clip = 'loop' 
        },

    },
    prop = {
        ['burger'] = { 
            model = `prop_cs_burger_01`, 
            pos = vec3(0.02, 0.02, -0.02), 
            rot = vec3(0.0, 0.0, 0.0) 
        },
        ['forks'] = {
            model = 'alcaprop_fork',
            bone = 57005,
            pos = vec3(0.14, 0.02, 0.01),
            rot = vec3(-118.0, 192.0, 24.0)
        }
    }
}