return {
	{
		coords = vector3(465.688, -970.712, 26.386),
		target = { -- qtarget support
			name = 'mrpd_evidence1', -- name of zone must be uniuqe
			loc = vec3(606.05, 9.26, 75.04),
			length = 1.4,
			width = 1,
			heading = 309,
			minZ = 1.08,
			maxZ = 5.08
		}
	},

	{
		coords = vector3(460.451, -970.712, 26.386),
		target = { -- qtarget support
			name = 'mrpd_evidence2', -- name of zone must be uniuqe
			loc = vec3(605.10, 6.63, 75.04),
			length = 1.4,
			width = 1,
			heading = 309,
			minZ = 1.28,
			maxZ = 5.28
		}
	},

	{
		coords = vector3(463.102, -972.514, 26.386),    --- MRPD
		target = { -- qtarget support
			name = 'mrpd_evidence3', -- name of zone must be uniuqe
			loc = vec3(460.95, -987.23, 26.39),
			length = 1.4,
			width = 1,
			heading = 182.75,
			minZ = 26.30,
			maxZ = 28.60
		}
	},

	{
		coords = vector3(2808.58, 4744.15, 47.89),    --- SAHP
		target = { -- qtarget support
			name = 'sahp_evidence', -- name of zone must be uniuqe
			loc = vec3(2808.58, 4744.15, 47.89),
			length = 1.4,
			width = 1,
			heading = 182.75,
			minZ = 26.30,
			maxZ = 28.60
		}
	},

	{
		coords = vector3(2800.77, 4748.65, 43.53),    --- SAHP
		target = { -- qtarget support
			name = 'sahp_evidence2', -- name of zone must be uniuqe
			loc = vec3(2800.77, 4748.65, 43.53),
			length = 1.4,
			width = 1,
			heading = 182.75,
			minZ = 26.30,
			maxZ = 28.60
		}
	},
}
